<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Base.Theme.MyInterest" parent="Theme.Material3.DayNight.NoActionBar">
        <!-- Primary Colors -->
        <item name="colorPrimary">@color/primary_blue</item>
        <item name="colorPrimaryVariant">@color/primary_blue_dark</item>
        <item name="colorOnPrimary">@color/text_white</item>

        <!-- Secondary Colors -->
        <item name="colorSecondary">@color/secondary_orange</item>
        <item name="colorSecondaryVariant">@color/secondary_orange_dark</item>
        <item name="colorOnSecondary">@color/text_white</item>

        <!-- Background Colors -->
        <item name="android:colorBackground">@color/background_light</item>
        <item name="colorSurface">@color/background_card</item>
        <item name="colorOnBackground">@color/text_primary</item>
        <item name="colorOnSurface">@color/text_primary</item>

        <!-- Status Bar -->
        <item name="android:statusBarColor">@color/primary_blue_dark</item>
        <item name="android:windowLightStatusBar">false</item>
    </style>

    <style name="Theme.MyInterest" parent="Base.Theme.MyInterest" />

    <!-- Card Style -->
    <style name="CardStyle">
        <item name="android:layout_margin">8dp</item>
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">4dp</item>
        <item name="android:background">@color/background_card</item>
    </style>

    <!-- Button Styles -->
    <style name="PrimaryButton" parent="Widget.Material3.Button">
        <item name="android:background">@color/primary_blue</item>
        <item name="android:textColor">@color/text_white</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>

    <style name="SecondaryButton" parent="Widget.Material3.Button.OutlinedButton">
        <item name="strokeColor">@color/primary_blue</item>
        <item name="android:textColor">@color/primary_blue</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:textSize">16sp</item>
    </style>

    <!-- Custom Popup Menu Style -->
    <style name="CustomPopupMenu" parent="Widget.Material3.PopupMenu">
        <item name="android:popupBackground">@drawable/popup_menu_background</item>
        <item name="android:textColor">@color/text_white</item>
        <item name="android:textColorPrimary">@color/text_white</item>
        <item name="android:colorBackground">@color/primary_blue_darker</item>
        <item name="android:dropDownListViewStyle">@style/CustomDropDownListView</item>
    </style>

    <!-- Custom DropDown ListView Style -->
    <style name="CustomDropDownListView">
        <item name="android:background">@color/primary_blue_darker</item>
        <item name="android:divider">@color/primary_blue_dark</item>
        <item name="android:dividerHeight">1dp</item>
    </style>

    <!-- Custom Menu Item Style -->
    <style name="CustomMenuItem">
        <item name="android:background">@color/primary_blue_darker</item>
        <item name="android:textColor">@color/text_white</item>
        <item name="android:padding">16dp</item>
        <item name="android:textSize">16sp</item>
    </style>

    <!-- Custom Toolbar Style -->
    <style name="CustomToolbar" parent="Widget.Material3.Toolbar">
        <item name="android:background">@color/primary_blue</item>
        <item name="titleTextColor">@color/text_white</item>
        <item name="android:theme">@style/ThemeOverlay.AppCompat.Dark.ActionBar</item>
        <item name="popupTheme">@style/CustomPopupMenu</item>
    </style>
</resources>