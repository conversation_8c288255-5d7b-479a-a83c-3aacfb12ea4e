/* ===== MODERN DASHBOARD STYLES ===== */

/* Tailwind-like utility classes */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.items-center {
  align-items: center;
}

.items-end {
  align-items: flex-end;
}

.justify-between {
  justify-content: space-between;
}

.justify-center {
  justify-content: center;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-3 {
  gap: 0.75rem;
}

.gap-4 {
  gap: 1rem;
}

.gap-6 {
  gap: 1.5rem;
}

.gap-8 {
  gap: 2rem;
}

.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.space-y-8>*+* {
  margin-top: 2rem;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-xs {
  font-size: 0.75rem;
}

.text-sm {
  font-size: 0.875rem;
}

.text-lg {
  font-size: 1.125rem;
}

.text-xl {
  font-size: 1.25rem;
}

.text-2xl {
  font-size: 1.5rem;
}

.text-4xl {
  font-size: 2.25rem;
}

.text-5xl {
  font-size: 3rem;
}

.font-medium {
  font-weight: 500;
}

.font-bold {
  font-weight: 700;
}

.text-gray-500 {
  color: #6b7280;
}

.text-gray-600 {
  color: #4b5563;
}

.text-gray-800 {
  color: #1f2937;
}

.text-white {
  color: #ffffff;
}

.text-green-600 {
  color: #059669;
}

.bg-white {
  background-color: #ffffff;
}

.bg-white\/80 {
  background-color: rgba(255, 255, 255, 0.8);
}

.bg-blue-500 {
  background-color: #3b82f6;
}

.bg-blue-600 {
  background-color: #2563eb;
}

.bg-red-500 {
  background-color: #ef4444;
}

.bg-red-600 {
  background-color: #dc2626;
}

.bg-purple-500 {
  background-color: #8b5cf6;
}

.bg-purple-600 {
  background-color: #7c3aed;
}

.bg-green-500 {
  background-color: #10b981;
}

.bg-green-600 {
  background-color: #059669;
}

.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.from-blue-600 {
  --tw-gradient-from: #2563eb;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(37, 99, 235, 0));
}

.via-purple-600 {
  --tw-gradient-stops: var(--tw-gradient-from), #7c3aed, var(--tw-gradient-to, rgba(124, 58, 237, 0));
}

.to-blue-800 {
  --tw-gradient-to: #1e40af;
}

.from-blue-500 {
  --tw-gradient-from: #3b82f6;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(59, 130, 246, 0));
}

.to-purple-500 {
  --tw-gradient-to: #8b5cf6;
}

.from-blue-50 {
  --tw-gradient-from: #eff6ff;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(239, 246, 255, 0));
}

.to-purple-50 {
  --tw-gradient-to: #faf5ff;
}

.from-gray-400 {
  --tw-gradient-from: #9ca3af;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(156, 163, 175, 0));
}

.to-gray-500 {
  --tw-gradient-to: #6b7280;
}

.from-blue-400\/10 {
  --tw-gradient-from: rgba(96, 165, 250, 0.1);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(96, 165, 250, 0));
}

.to-purple-400\/10 {
  --tw-gradient-to: rgba(196, 181, 253, 0.1);
}

.from-blue-500\/10 {
  --tw-gradient-from: rgba(59, 130, 246, 0.1);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(59, 130, 246, 0));
}

.to-purple-500\/10 {
  --tw-gradient-to: rgba(139, 92, 246, 0.1);
}

.from-purple-500 {
  --tw-gradient-from: #8b5cf6;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(139, 92, 246, 0));
}

.to-pink-500 {
  --tw-gradient-to: #ec4899;
}

.from-green-500 {
  --tw-gradient-from: #10b981;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(16, 185, 129, 0));
}

.to-green-600 {
  --tw-gradient-to: #059669;
}

.bg-clip-text {
  -webkit-background-clip: text;
  background-clip: text;
}

.text-transparent {
  color: transparent;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.rounded-xl {
  border-radius: 0.75rem;
}

.rounded-2xl {
  border-radius: 1rem;
}

.rounded-full {
  border-radius: 9999px;
}

.border {
  border-width: 1px;
}

.border-gray-200 {
  border-color: #e5e7eb;
}

.shadow-lg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.shadow-xl {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.backdrop-blur-sm {
  backdrop-filter: blur(4px);
}

.p-4 {
  padding: 1rem;
}

.p-6 {
  padding: 1.5rem;
}

.p-8 {
  padding: 2rem;
}

.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.m-4 {
  margin: 1rem;
}

.mb-3 {
  margin-bottom: 0.75rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mt-6 {
  margin-top: 1.5rem;
}

.w-8 {
  width: 2rem;
}

.w-12 {
  width: 3rem;
}

.w-20 {
  width: 5rem;
}

.h-1 {
  height: 0.25rem;
}

.h-8 {
  height: 2rem;
}

.h-12 {
  height: 3rem;
}

.max-w-2xl {
  max-width: 42rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.duration-300 {
  transition-duration: 300ms;
}

.hover\:bg-blue-600:hover {
  background-color: #2563eb;
}

.hover\:bg-red-600:hover {
  background-color: #dc2626;
}

.hover\:shadow-xl:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.hover\:transform:hover {
  transform: var(--tw-transform);
}

.hover\:scale-\[1\.02\]:hover {
  --tw-scale-x: 1.02;
  --tw-scale-y: 1.02;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.flex-1 {
  flex: 1 1 0%;
}

/* Additional utility classes */
.cursor-pointer {
  cursor: pointer;
}

.overflow-hidden {
  overflow: hidden;
}

.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.top-0 {
  top: 0;
}

.left-0 {
  left: 0;
}

.right-0 {
  right: 0;
}

.bottom-0 {
  bottom: 0;
}

.z-10 {
  z-index: 10;
}

.opacity-0 {
  opacity: 0;
}

.opacity-100 {
  opacity: 1;
}

.transform {
  transform: var(--tw-transform);
}

.scale-100 {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-0 {
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-1 {
  --tw-translate-y: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

/* Button styles */
button {
  cursor: pointer;
  border: none;
  outline: none;
  font-family: inherit;
}

button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Additional utility classes needed */
.h-full {
  height: 100%;
}

.border-r {
  border-right-width: 1px;
}

.border-b {
  border-bottom-width: 1px;
}

.border-t {
  border-top-width: 1px;
}

.border-2 {
  border-width: 2px;
}

.border-3 {
  border-width: 3px;
}

.border-gray-100 {
  border-color: #f3f4f6;
}

.border-blue-200 {
  border-color: #dbeafe;
}

.border-blue-300 {
  border-color: #93c5fd;
}

.border-blue-400 {
  border-color: #60a5fa;
}

.bg-blue-50 {
  background-color: #eff6ff;
}

.bg-blue-100 {
  background-color: #dbeafe;
}

.bg-blue-200 {
  background-color: #bfdbfe;
}

.bg-purple-50 {
  background-color: #faf5ff;
}

.bg-gray-50 {
  background-color: #f9fafb;
}

.bg-gray-100 {
  background-color: #f3f4f6;
}

.bg-gray-200 {
  background-color: #e5e7eb;
}

.bg-gray-300 {
  background-color: #d1d5db;
}

.bg-gray-400 {
  background-color: #9ca3af;
}

.bg-gray-500 {
  background-color: #6b7280;
}

.bg-gray-50\/80 {
  background-color: rgba(249, 250, 251, 0.8);
}

.bg-white\/70 {
  background-color: rgba(255, 255, 255, 0.7);
}

.text-blue-600 {
  color: #2563eb;
}

.text-blue-700 {
  color: #1d4ed8;
}

.text-blue-800 {
  color: #1e40af;
}

.text-gray-400 {
  color: #9ca3af;
}

.text-gray-700 {
  color: #374151;
}

.font-semibold {
  font-weight: 600;
}

.min-w-0 {
  min-width: 0;
}

.min-w-\[24px\] {
  min-width: 24px;
}

.w-5 {
  width: 1.25rem;
}

.w-10 {
  width: 2.5rem;
}

.w-full {
  width: 100%;
}

.h-5 {
  height: 1.25rem;
}

.h-6 {
  height: 1.5rem;
}

.h-10 {
  height: 2.5rem;
}

.pl-3 {
  padding-left: 0.75rem;
}

.pl-10 {
  padding-left: 2.5rem;
}

.pr-3 {
  padding-right: 0.75rem;
}

.pr-10 {
  padding-right: 2.5rem;
}

.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.space-y-2>*+* {
  margin-top: 0.5rem;
}

.overflow-y-auto {
  overflow-y: auto;
}

.flex-1 {
  flex: 1 1 0%;
}

.inset-y-0 {
  top: 0;
  bottom: 0;
}

.inset-0 {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.pointer-events-none {
  pointer-events: none;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Animation classes */
.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-pulse-slow {
  animation: pulse 3s infinite;
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out;
}

.animate-bounce-subtle {
  animation: bounce-subtle 2s infinite;
}

/* Focus and ring utilities */
.focus\:border-blue-400:focus {
  border-color: #60a5fa;
}

.focus\:ring-4:focus {
  box-shadow: 0 0 0 4px var(--ring-color);
}

.focus\:ring-blue-100:focus {
  --ring-color: rgba(219, 234, 254, 0.5);
}

/* Hover utilities */
.hover\:text-gray-600:hover {
  color: #4b5563;
}

.hover\:bg-gray-300:hover {
  background-color: #d1d5db;
}

.hover\:bg-blue-50:hover {
  background-color: #eff6ff;
}

.hover\:bg-blue-200:hover {
  background-color: #bfdbfe;
}

.hover\:border-blue-300:hover {
  border-color: #93c5fd;
}

.hover\:text-blue-600:hover {
  color: #2563eb;
}

.hover\:shadow-md:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.hover\:scale-\[1\.01\]:hover {
  --tw-scale-x: 1.01;
  --tw-scale-y: 1.01;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.active\:scale-\[0\.98\]:active {
  --tw-scale-x: 0.98;
  --tw-scale-y: 0.98;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

/* Group hover effects */
.group:hover .group-hover\:translate-x-0 {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

/* Transform utilities */
.-translate-x-full {
  --tw-translate-x: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-\[1\.02\] {
  --tw-scale-x: 1.02;
  --tw-scale-y: 1.02;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

/* Additional background utilities */
.bg-blue-400\/10 {
  background-color: rgba(96, 165, 250, 0.1);
}

.bg-purple-400\/10 {
  background-color: rgba(196, 181, 253, 0.1);
}

/* Additional missing classes */
.group {
  position: relative;
}

.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.duration-200 {
  transition-duration: 200ms;
}

.duration-500 {
  transition-duration: 500ms;
}

.text-base {
  font-size: 1rem;
}

.opacity-50 {
  opacity: 0.5;
}

.border-t-blue-500 {
  border-top-color: #3b82f6;
}

.rounded-md {
  border-radius: 0.375rem;
}

.z-10 {
  z-index: 10;
}

.rounded-r {
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}

.from-blue-500\/10 {
  --tw-gradient-from: rgba(59, 130, 246, 0.1);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(59, 130, 246, 0));
}

.to-purple-500\/10 {
  --tw-gradient-to: rgba(139, 92, 246, 0.1);
}

.bg-gradient-to-b {
  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));
}

/* Shadow utilities */
.shadow-sm {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.shadow-md {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Keyframes for animations */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounce-subtle {

  0%,
  100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-2px);
  }
}

/* Input styles */
input {
  font-family: inherit;
}

input:focus {
  outline: none;
}

/* Responsive utilities */
@media (min-width: 768px) {
  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

@media (min-width: 1024px) {
  .lg\:flex-row {
    flex-direction: row;
  }

  .lg\:items-center {
    align-items: center;
  }

  .lg\:justify-between {
    justify-content: space-between;
  }

  .lg\:text-left {
    text-align: left;
  }

  .lg\:text-5xl {
    font-size: 3rem;
  }

  .lg\:p-8 {
    padding: 2rem;
  }

  .lg\:mt-0 {
    margin-top: 0;
  }

  .lg\:mx-0 {
    margin-left: 0;
    margin-right: 0;
  }

  .lg\:items-end {
    align-items: flex-end;
  }
}

/* Main Dashboard Container */
.dashboard-container {
  display: grid;
  grid-template-columns: 280px 1fr;
  min-height: 100vh;
  background: var(--bg-secondary);
  font-family: var(--font-family-primary);
  gap: 0;
  position: relative;
}

/* Responsive Grid */
@media (max-width: 1024px) {
  .dashboard-container {
    grid-template-columns: 260px 1fr;
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr;
  }
}

/* ===== SIDEBAR STYLES ===== */
.sidebar {
  background: linear-gradient(180deg, var(--bg-primary) 0%, var(--neutral-50) 100%);
  border-right: 1px solid var(--border-light);
  padding: var(--space-xl);
  position: relative;
  overflow-y: auto;
  box-shadow: var(--shadow-lg);
  backdrop-filter: blur(10px);
}

.sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--bg-accent);
}

.sidebar-title {
  font-size: var(--text-xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-xl);
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.sidebar-title::before {
  content: '📊';
  font-size: var(--text-2xl);
}

/* Mobile Sidebar */
@media (max-width: 768px) {
  .sidebar {
    padding: var(--space-lg);
    border-right: none;
    border-bottom: 1px solid var(--border-light);
  }
}

/* ===== MAIN CONTENT STYLES ===== */
.main-content {
  padding: var(--space-2xl);
  overflow-y: auto;
  background: var(--bg-secondary);
  position: relative;
}

.main-content::before {
  content: '';
  position: fixed;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 80%, rgba(14, 165, 233, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(217, 70, 239, 0.1) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

/* Dashboard Title */
.dashboard-title {
  font-size: var(--text-4xl);
  font-weight: 800;
  color: var(--text-primary);
  margin-bottom: var(--space-2xl);
  background: var(--bg-accent);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
  display: inline-block;
}

.dashboard-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 60px;
  height: 4px;
  background: var(--bg-accent);
  border-radius: var(--radius-full);
}

/* Mobile Main Content */
@media (max-width: 768px) {
  .main-content {
    padding: var(--space-lg);
  }

  .dashboard-title {
    font-size: var(--text-3xl);
  }
}

/* ===== DASHBOARD SECTIONS ===== */
.dashboard-section {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-2xl);
  margin-bottom: var(--space-2xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-light);
  position: relative;
  overflow: hidden;
  transition: all var(--transition-normal);
}

.dashboard-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--bg-accent);
}

.dashboard-section:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

/* Section Headers */
.dashboard-section h2 {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-lg);
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.dashboard-section h3 {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-md);
}

/* ===== BUTTON STYLES ===== */
.button-group {
  display: flex;
  gap: var(--space-md);
  margin-top: var(--space-xl);
  flex-wrap: wrap;
}

/* Primary Button */
.btn-primary {
  background: var(--bg-accent);
  color: var(--text-inverse);
  border: none;
  padding: var(--space-md) var(--space-xl);
  border-radius: var(--radius-lg);
  font-weight: 600;
  font-size: var(--text-sm);
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--transition-slow);
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

.btn-primary:active {
  transform: translateY(0);
}

/* Secondary Button */
.btn-secondary {
  background: var(--bg-primary);
  color: var(--primary-600);
  border: 2px solid var(--primary-200);
  padding: calc(var(--space-md) - 2px) calc(var(--space-xl) - 2px);
  border-radius: var(--radius-lg);
  font-weight: 600;
  font-size: var(--text-sm);
  cursor: pointer;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
  background: var(--primary-50);
  border-color: var(--primary-300);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Icon Button */
.btn-icon {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--space-md);
  cursor: pointer;
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-sm);
}

.btn-icon:hover {
  background: var(--neutral-50);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* ===== TABLE STYLES ===== */
table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
}

th {
  background: linear-gradient(135deg, var(--neutral-50) 0%, var(--neutral-100) 100%);
  color: var(--text-primary);
  font-weight: 600;
  font-size: var(--text-sm);
  padding: var(--space-lg) var(--space-xl);
  text-align: left;
  border-bottom: 2px solid var(--border-light);
  position: relative;
}

th::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--bg-accent);
}

td {
  padding: var(--space-lg) var(--space-xl);
  border-bottom: 1px solid var(--border-light);
  color: var(--text-secondary);
  font-size: var(--text-sm);
  transition: background-color var(--transition-fast);
}

tr:hover td {
  background: var(--primary-50);
}

tr:last-child td {
  border-bottom: none;
}

/* ===== DEVELOPER INFO STYLES ===== */
.developer-info {
  position: absolute;
  top: var(--space-2xl);
  right: var(--space-2xl);
  z-index: 10;
}

.developer-card {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-light);
  min-width: 280px;
}

.developer-details {
  text-align: right;
  margin-bottom: var(--space-lg);
}

.welcome-text {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin-bottom: var(--space-xs);
}

.developer-name {
  font-size: var(--text-lg);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-xs);
}

.company-info {
  font-size: var(--text-xs);
  color: var(--text-secondary);
}

/* Mobile Developer Info */
@media (max-width: 768px) {
  .developer-info {
    position: static;
    margin-bottom: var(--space-xl);
  }

  .developer-card {
    min-width: auto;
  }
}