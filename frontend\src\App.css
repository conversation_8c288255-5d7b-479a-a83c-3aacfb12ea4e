/* ===== MODERN DASHBOARD STYLES ===== */

/* Main Dashboard Container */
.dashboard-container {
  display: grid;
  grid-template-columns: 280px 1fr;
  min-height: 100vh;
  background: var(--bg-secondary);
  font-family: var(--font-family-primary);
  gap: 0;
  position: relative;
}

/* Responsive Grid */
@media (max-width: 1024px) {
  .dashboard-container {
    grid-template-columns: 260px 1fr;
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr;
  }
}

/* ===== SIDEBAR STYLES ===== */
.sidebar {
  background: linear-gradient(180deg, var(--bg-primary) 0%, var(--neutral-50) 100%);
  border-right: 1px solid var(--border-light);
  padding: var(--space-xl);
  position: relative;
  overflow-y: auto;
  box-shadow: var(--shadow-lg);
  backdrop-filter: blur(10px);
}

.sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--bg-accent);
}

.sidebar-title {
  font-size: var(--text-xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-xl);
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.sidebar-title::before {
  content: '📊';
  font-size: var(--text-2xl);
}

/* Mobile Sidebar */
@media (max-width: 768px) {
  .sidebar {
    padding: var(--space-lg);
    border-right: none;
    border-bottom: 1px solid var(--border-light);
  }
}

/* ===== MAIN CONTENT STYLES ===== */
.main-content {
  padding: var(--space-2xl);
  overflow-y: auto;
  background: var(--bg-secondary);
  position: relative;
}

.main-content::before {
  content: '';
  position: fixed;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 80%, rgba(14, 165, 233, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(217, 70, 239, 0.1) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

/* Dashboard Title */
.dashboard-title {
  font-size: var(--text-4xl);
  font-weight: 800;
  color: var(--text-primary);
  margin-bottom: var(--space-2xl);
  background: var(--bg-accent);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
  display: inline-block;
}

.dashboard-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 60px;
  height: 4px;
  background: var(--bg-accent);
  border-radius: var(--radius-full);
}

/* Mobile Main Content */
@media (max-width: 768px) {
  .main-content {
    padding: var(--space-lg);
  }

  .dashboard-title {
    font-size: var(--text-3xl);
  }
}

/* ===== DASHBOARD SECTIONS ===== */
.dashboard-section {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-2xl);
  margin-bottom: var(--space-2xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-light);
  position: relative;
  overflow: hidden;
  transition: all var(--transition-normal);
}

.dashboard-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--bg-accent);
}

.dashboard-section:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

/* Section Headers */
.dashboard-section h2 {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-lg);
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.dashboard-section h3 {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-md);
}

/* ===== BUTTON STYLES ===== */
.button-group {
  display: flex;
  gap: var(--space-md);
  margin-top: var(--space-xl);
  flex-wrap: wrap;
}

/* Primary Button */
.btn-primary {
  background: var(--bg-accent);
  color: var(--text-inverse);
  border: none;
  padding: var(--space-md) var(--space-xl);
  border-radius: var(--radius-lg);
  font-weight: 600;
  font-size: var(--text-sm);
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--transition-slow);
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

.btn-primary:active {
  transform: translateY(0);
}

/* Secondary Button */
.btn-secondary {
  background: var(--bg-primary);
  color: var(--primary-600);
  border: 2px solid var(--primary-200);
  padding: calc(var(--space-md) - 2px) calc(var(--space-xl) - 2px);
  border-radius: var(--radius-lg);
  font-weight: 600;
  font-size: var(--text-sm);
  cursor: pointer;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
  background: var(--primary-50);
  border-color: var(--primary-300);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Icon Button */
.btn-icon {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--space-md);
  cursor: pointer;
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-sm);
}

.btn-icon:hover {
  background: var(--neutral-50);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* ===== TABLE STYLES ===== */
table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
}

th {
  background: linear-gradient(135deg, var(--neutral-50) 0%, var(--neutral-100) 100%);
  color: var(--text-primary);
  font-weight: 600;
  font-size: var(--text-sm);
  padding: var(--space-lg) var(--space-xl);
  text-align: left;
  border-bottom: 2px solid var(--border-light);
  position: relative;
}

th::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--bg-accent);
}

td {
  padding: var(--space-lg) var(--space-xl);
  border-bottom: 1px solid var(--border-light);
  color: var(--text-secondary);
  font-size: var(--text-sm);
  transition: background-color var(--transition-fast);
}

tr:hover td {
  background: var(--primary-50);
}

tr:last-child td {
  border-bottom: none;
}