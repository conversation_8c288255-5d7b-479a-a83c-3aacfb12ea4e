<!-- res/layout/item_card.xml -->
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="12dp"
    app:cardElevation="8dp"
    app:cardCornerRadius="16dp"
    android:clickable="true"
    android:foreground="?attr/selectableItemBackground"
    app:cardBackgroundColor="@color/background_card">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="20dp"
        android:gravity="center_horizontal">

        <!-- Icon Background Circle -->
        <androidx.cardview.widget.CardView
            android:layout_width="80dp"
            android:layout_height="80dp"
            app:cardCornerRadius="40dp"
            app:cardElevation="4dp"
            android:layout_marginBottom="12dp"
            app:cardBackgroundColor="@color/primary_blue_light">

            <ImageView
                android:id="@+id/itemImage"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_gravity="center"
                android:contentDescription="תמונה"
                android:scaleType="centerInside" />

        </androidx.cardview.widget.CardView>

        <TextView
            android:id="@+id/itemName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="שם פריט"
            android:textSize="16sp"
            android:textColor="@color/text_primary"
            android:textStyle="bold"
            android:fontFamily="sans-serif-medium"
            android:gravity="center" />

    </LinearLayout>
</androidx.cardview.widget.CardView>
