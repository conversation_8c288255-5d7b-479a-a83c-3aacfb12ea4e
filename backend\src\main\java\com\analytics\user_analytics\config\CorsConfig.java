package com.analytics.user_analytics.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class CorsConfig {

    @Bean
    public WebMvcConfigurer corsConfigurer() {
        return new WebMvcConfigurer() {
            @Override
            public void addCorsMappings(CorsRegistry registry) {
                registry.addMapping("/**")
                        .allowedOrigins("http://localhost:3000",
                                "http://my-react-frontend-bucket1.s3-website.eu-north-1.amazonaws.com",
                                "https://d1xb34m3k0zeus.cloudfront.net",
                                "http://user-analytics-backend-env.eba-kc7wz3xt.eu-north-1.elasticbeanstalk.com",
                                "https://user-analytics-backend-env.eba-kc7wz3xt.eu-north-1.elasticbeanstalk.com")
                        .allowedMethods("*")
                        .allowedHeaders("*")
                        .allowCredentials(true);

            }
        };
    }

}
