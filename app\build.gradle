//app/build.gradle
plugins {
    alias(libs.plugins.android.application)
}

def localProps = new Properties()
def localPropsFile = rootProject.file("local.properties")
if (localPropsFile.exists()) {
    localProps.load(new FileInputStream(localPropsFile))
}

android {
    namespace 'com.analytics.analyticsfinal'
    compileSdk 35

    defaultConfig {
        applicationId "com.analytics.analyticsfinal"
        minSdk 26
        targetSdk 35
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        // 💡 קונפיגורציית הסודות
        buildConfigField "String", "ANALYTICS_BASE_URL", "\"${localProps['ANALYTICS_BASE_URL']}\""
        buildConfigField "String", "ANALYTICS_API_KEY", "\"${localProps['ANALYTICS_API_KEY']}\""
    }

    buildFeatures {
        buildConfig true
    }


    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
}

dependencies {

    implementation libs.appcompat
    implementation libs.material
    implementation libs.activity
    implementation libs.constraintlayout
    testImplementation libs.junit
    androidTestImplementation libs.ext.junit
    androidTestImplementation libs.espresso.core





    // jitpack
    implementation 'com.github.nSella10:UserAnalyticsSDK:v1.0.3'


    // Retrofit
    implementation libs.retrofit

// ממיר JSON באמצעות Gson
    implementation libs.converter.gson
}