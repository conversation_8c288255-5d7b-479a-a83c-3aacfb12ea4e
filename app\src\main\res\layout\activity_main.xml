<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/mainToolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary_blue"
        android:title="MyInterest"
        android:titleTextColor="@color/text_white"
        android:elevation="4dp"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        app:popupTheme="@style/CustomPopupMenu"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- Welcome Section -->
    <LinearLayout
        android:id="@+id/welcomeSection"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="20dp"
        android:background="@color/primary_blue_light"
        app:layout_constraintTop_toBottomOf="@id/mainToolbar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="ברוכים הבאים ל-MyInterest!"
            android:textSize="24sp"
            android:textStyle="bold"
            android:textColor="@color/primary_blue_dark"
            android:fontFamily="sans-serif-medium" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="בחרו את הקטגוריות שמעניינות אתכם"
            android:textSize="16sp"
            android:textColor="@color/text_secondary"
            android:fontFamily="sans-serif" />

    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/categoryRecycler"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:padding="16dp"
        android:clipToPadding="false"
        app:layout_constraintTop_toBottomOf="@id/welcomeSection"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
        app:spanCount="2"/>

</androidx.constraintlayout.widget.ConstraintLayout>
