<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    <!-- הרשאה לחיבור לאינטרנט -->
    <uses-permission android:name="android.permission.INTERNET" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.MyInterest"
        tools:targetApi="31">
        <activity
            android:name=".pages.UserProfileActivity"
            android:exported="false" />
        <activity
            android:name=".pages.ItemDetailActivity"
            android:exported="false" />
        <activity
            android:name=".pages.CategoryActivity"
            android:exported="false" /> <!-- AuthActivity -->
        <activity
            android:name=".pages.AuthActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".MainActivity"
            android:exported="false" />
    </application>

</manifest>