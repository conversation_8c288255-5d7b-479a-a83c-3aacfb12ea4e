plugins {
    alias(libs.plugins.android.library)
}

android {
    namespace 'com.analytics.analyticstracker'
    compileSdk 35

    defaultConfig {
        minSdk 26

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
}

dependencies {

    implementation libs.appcompat
    implementation libs.material
    testImplementation libs.junit
    androidTestImplementation libs.ext.junit
    androidTestImplementation libs.espresso.core

    // Retrofit
    implementation libs.retrofit

// ממיר JSON באמצעות Gson
    implementation libs.converter.gson

    implementation libs.logging.interceptor




}